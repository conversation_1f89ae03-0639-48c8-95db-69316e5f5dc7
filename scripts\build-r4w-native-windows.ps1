# r4w-linux Native Windows Builder
# PowerShell Script zum Erstellen eines konfigurierten Kali Linux Images
# Version: 0.1.0 - Native Windows ohne WSL2
# Autor: r4w-linux project
# Benötigt: Windows 10/11 mit Administrator-Rechten

param(
    [Parameter(Mandatory=$false)]
    [string]$KaliImage = "",
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipChecks = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$VerboseOutput = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$KeepTempFiles = $false
)

$ErrorActionPreference = "Stop"
$version = "0.1.0"

Write-Host "🐍 r4w-linux Native Windows Builder v$version" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green
Write-Host "⚡ Kein WSL2 erforderlich - Native Windows Build!" -ForegroundColor Cyan
Write-Host ""

# Administrator-Rechte prüfen
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ Administrator-Rechte erforderlich!" -ForegroundColor Red
    Write-Host "💡 Starte PowerShell als Administrator und führe das Script erneut aus." -ForegroundColor Yellow
    exit 1
}

# Prüfe ob 7-Zip verfügbar ist
Write-Host "🔍 Prüfe 7-Zip..." -ForegroundColor Yellow
$sevenZipPaths = @(
    "${env:ProgramFiles}\7-Zip\7z.exe",
    "${env:ProgramFiles(x86)}\7-Zip\7z.exe",
    "7z.exe"
)

$sevenZipPath = $null
foreach ($path in $sevenZipPaths) {
    if (Test-Path $path -ErrorAction SilentlyContinue) {
        $sevenZipPath = $path
        break
    }
}

if (-not $sevenZipPath) {
    Write-Host "❌ 7-Zip nicht gefunden!" -ForegroundColor Red
    Write-Host "💡 Installiere 7-Zip von: https://www.7-zip.org/" -ForegroundColor Yellow
    Write-Host "   Oder installiere mit: winget install 7zip.7zip" -ForegroundColor White
    exit 1
}

Write-Host "✅ 7-Zip gefunden: $sevenZipPath" -ForegroundColor Green

# Prüfe ob Kali Image vorhanden
Write-Host "🔍 Suche Kali Linux Image..." -ForegroundColor Yellow
if ($KaliImage -eq "") {
    # Suche nach .img.xz und .img Dateien
    $kaliFiles = @()
    $kaliFiles += Get-ChildItem -Path . -Filter "kali-linux-*-raspberry-pi-zero-w.img.xz" -ErrorAction SilentlyContinue
    $kaliFiles += Get-ChildItem -Path . -Filter "kali-linux-*-raspberry-pi-zero-w.img" -ErrorAction SilentlyContinue

    if ($kaliFiles.Count -eq 0) {
        Write-Host "❌ Kein Kali Linux Image gefunden!" -ForegroundColor Red
        Write-Host "💡 Lade es von: https://www.kali.org/get-kali/#kali-arm" -ForegroundColor Yellow
        Write-Host "   Dateiname sollte sein: kali-linux-*-raspberry-pi-zero-w.img.xz oder .img" -ForegroundColor White
        exit 1
    }
    $KaliImage = $kaliFiles[0].Name
}

if (-not (Test-Path $KaliImage)) {
    Write-Host "❌ Kali Image nicht gefunden: $KaliImage" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Kali Image gefunden: $KaliImage" -ForegroundColor Green

# Konfiguration
$workDir = "r4w_build_windows"
$outputImg = "r4w-linux.img"
$tempImg = "temp_kali.img"
$versionFile = "VERSION"
$projectVersion = if (Test-Path $versionFile) { Get-Content $versionFile } else { "0.1.0" }

Write-Host "🔧 Konfiguration:" -ForegroundColor Blue
Write-Host "   Arbeitsverzeichnis: $workDir" -ForegroundColor White
Write-Host "   Output Image: $outputImg" -ForegroundColor White
Write-Host "   Version: $projectVersion" -ForegroundColor White
Write-Host ""

# Cleanup function
function Cleanup {
    Write-Host "🧹 Cleanup wird ausgeführt..." -ForegroundColor Yellow
    
    # Dismount any mounted images
    try {
        $mountedImages = Get-DiskImage -ErrorAction SilentlyContinue | Where-Object { $_.ImagePath -like "*$tempImg*" -or $_.ImagePath -like "*$outputImg*" }
        if ($mountedImages) {
            $mountedImages | Dismount-DiskImage -ErrorAction SilentlyContinue
        }
    } catch {
        # Ignore errors during cleanup
    }
    
    if (-not $KeepTempFiles) {
        if (Test-Path $workDir) {
            Remove-Item -Path $workDir -Recurse -Force -ErrorAction SilentlyContinue
        }
        if (Test-Path $tempImg) {
            Remove-Item -Path $tempImg -Force -ErrorAction SilentlyContinue
        }
    }
}

# Set trap for cleanup on exit
trap { Cleanup; exit 1 }

# Workspace vorbereiten
Write-Host "🧹 Workspace vorbereiten..." -ForegroundColor Yellow
Cleanup
New-Item -ItemType Directory -Path $workDir -Force | Out-Null

# Image entpacken
Write-Host "📦 Kali Image entpacken..." -ForegroundColor Yellow
Write-Host "   Dies kann einige Minuten dauern..." -ForegroundColor Gray

try {
    & $sevenZipPath x "$KaliImage" -o"." -y | Out-Null
    $extractedImg = (Get-ChildItem -Filter "kali-linux-*-raspberry-pi-zero-w.img").Name
    Rename-Item $extractedImg $tempImg
    Write-Host "✅ Image entpackt: $tempImg" -ForegroundColor Green
} catch {
    Write-Host "❌ Fehler beim Entpacken: $_" -ForegroundColor Red
    exit 1
}

# Image als Disk mounten
Write-Host "💾 Image als Disk mounten..." -ForegroundColor Yellow
try {
    $mountResult = Mount-DiskImage -ImagePath (Resolve-Path $tempImg).Path -PassThru
    $disk = $mountResult | Get-Disk
    $partitions = $disk | Get-Partition | Sort-Object PartitionNumber
    
    if ($partitions.Count -lt 2) {
        throw "Nicht genügend Partitionen gefunden"
    }
    
    $bootPartition = $partitions[0]
    $rootPartition = $partitions[1]
    
    Write-Host "✅ Disk gemountet - Disk Number: $($disk.Number)" -ForegroundColor Green
    Write-Host "   Boot Partition: $($bootPartition.DriveLetter):" -ForegroundColor White
    Write-Host "   Root Partition: $($rootPartition.DriveLetter):" -ForegroundColor White
    
} catch {
    Write-Host "❌ Fehler beim Mounten: $_" -ForegroundColor Red
    Cleanup
    exit 1
}

# Boot-Partition konfigurieren
Write-Host "🔧 Boot-Partition konfigurieren..." -ForegroundColor Yellow
$bootDrive = "$($bootPartition.DriveLetter):"

try {
    # SSH aktivieren
    New-Item -Path "$bootDrive\ssh" -ItemType File -Force | Out-Null
    Write-Host "✅ SSH aktiviert" -ForegroundColor Green
    
    # WLAN-Konfiguration
    $wpaConfig = @"
country=DE
ctrl_interface=DIR=/var/run/wpa_supplicant GROUP=netdev
update_config=1

network={
    ssid="WLAN_NAME"
    psk="WLAN_PASSWORT"
    key_mgmt=WPA-PSK
}
"@
    Set-Content -Path "$bootDrive\wpa_supplicant.conf" -Value $wpaConfig -Encoding UTF8
    Write-Host "✅ WLAN-Konfiguration erstellt" -ForegroundColor Green
    
    # Statische IP-Konfiguration
    $dhcpcdConfig = @"
# Statische IP für r4w
interface wlan0
static ip_address=************/24
static routers=***********
static domain_name_servers=******* *******
"@
    Set-Content -Path "$bootDrive\dhcpcd.conf.append" -Value $dhcpcdConfig -Encoding UTF8
    Write-Host "✅ Statische IP-Konfiguration erstellt" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Fehler bei Boot-Konfiguration: $_" -ForegroundColor Red
    Cleanup
    exit 1
}

Write-Host ""
Write-Host "⚠️  WICHTIGER HINWEIS:" -ForegroundColor Yellow
Write-Host "   Das Root-Filesystem kann unter Windows nicht vollständig" -ForegroundColor White
Write-Host "   modifiziert werden (ext4 wird nicht nativ unterstützt)." -ForegroundColor White
Write-Host "   Für vollständige Konfiguration verwende das Linux Build-Script" -ForegroundColor White
Write-Host "   oder führe die Konfiguration nach dem ersten Boot durch." -ForegroundColor White
Write-Host ""

# Image dismounten
Write-Host "💾 Image dismounten..." -ForegroundColor Yellow
try {
    Dismount-DiskImage -ImagePath (Resolve-Path $tempImg).Path
    Write-Host "✅ Image dismounted" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Warnung beim Dismounten: $_" -ForegroundColor Yellow
}

# Finales Image erstellen
Write-Host "📦 Finales Image erstellen..." -ForegroundColor Yellow
Copy-Item $tempImg $outputImg -Force
Write-Host "✅ Image kopiert: $outputImg" -ForegroundColor Green

# Image komprimieren
Write-Host "📦 Image komprimieren..." -ForegroundColor Yellow
Write-Host "   Dies kann mehrere Minuten dauern..." -ForegroundColor Gray
try {
    & $sevenZipPath a -txz -mx=9 "$outputImg.xz" $outputImg | Out-Null
    Write-Host "✅ Image komprimiert: $outputImg.xz" -ForegroundColor Green
} catch {
    Write-Host "❌ Fehler beim Komprimieren: $_" -ForegroundColor Red
    Cleanup
    exit 1
}

# Checksumme erstellen
Write-Host "🔐 Checksumme erstellen..." -ForegroundColor Yellow
try {
    $hash = Get-FileHash "$outputImg.xz" -Algorithm SHA256
    "$($hash.Hash.ToLower())  $outputImg.xz" | Set-Content "$outputImg.xz.sha256" -Encoding ASCII
    Write-Host "✅ Checksumme erstellt: $outputImg.xz.sha256" -ForegroundColor Green
} catch {
    Write-Host "❌ Fehler bei Checksumme: $_" -ForegroundColor Red
}

# Cleanup
if (-not $KeepTempFiles) {
    Remove-Item $tempImg -Force -ErrorAction SilentlyContinue
    Remove-Item $outputImg -Force -ErrorAction SilentlyContinue
}

Write-Host ""
Write-Host "🎉 r4w-linux v$projectVersion erfolgreich erstellt!" -ForegroundColor Green
Write-Host "📁 Dateien:" -ForegroundColor Blue
Write-Host "   - $outputImg.xz" -ForegroundColor White
Write-Host "   - $outputImg.xz.sha256" -ForegroundColor White
Write-Host ""
Write-Host "📋 Nächste Schritte:" -ForegroundColor Yellow
Write-Host "   1. Image mit Balena Etcher flashen" -ForegroundColor White
Write-Host "   2. WLAN in wpa_supplicant.conf konfigurieren" -ForegroundColor White
Write-Host "   3. SSH-Key nach dem ersten Boot konfigurieren" -ForegroundColor White
Write-Host "   4. ssh kali@************" -ForegroundColor White
Write-Host ""
Write-Host "⚠️  Hinweis: Für vollständige r4w-Konfiguration führe nach dem" -ForegroundColor Yellow
Write-Host "   ersten Boot das Setup-Script auf dem Pi aus:" -ForegroundColor Yellow
Write-Host "   sudo /home/<USER>/extras/autostart.sh" -ForegroundColor White
Write-Host ""
