# r4w-linux Makefile
# Automatisiert Build- und Setup-Prozesse
# Version: 0.1.0

.PHONY: help build clean setup-ssh flash-help install-deps check-deps

# Konfiguration
VERSION := $(shell cat VERSION 2>/dev/null || echo "0.0.1")
IMAGE_NAME := r4w-linux.img
COMPRESSED_IMAGE := $(IMAGE_NAME).xz
KALI_IMAGE := kali-linux-*-raspberry-pi-zero-w.img.xz

# Farben für Output
GREEN := \033[0;32m
YELLOW := \033[1;33m
BLUE := \033[0;34m
RED := \033[0;31m
NC := \033[0m # No Color

help: ## Zeigt diese Hilfe an
	@echo "$(GREEN)🐍 r4w-linux Build System v$(VERSION)$(NC)"
	@echo "$(GREEN)=====================================$(NC)"
	@echo ""
	@echo "Verfügbare Targets:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(BLUE)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "$(YELLOW)Beispiel:$(NC)"
	@echo "  make check-deps    # Abhängigkeiten prüfen"
	@echo "  make build         # Image erstellen"
	@echo "  make setup-ssh     # SSH konfigurieren"

check-deps: ## Prüft ob alle Abhängigkeiten installiert sind
	@echo "$(BLUE)🔍 Prüfe Abhängigkeiten...$(NC)"
	@command -v kpartx >/dev/null 2>&1 || (echo "$(RED)❌ kpartx nicht gefunden$(NC)" && exit 1)
	@command -v xzcat >/dev/null 2>&1 || (echo "$(RED)❌ xzcat nicht gefunden$(NC)" && exit 1)
	@command -v sha256sum >/dev/null 2>&1 || (echo "$(RED)❌ sha256sum nicht gefunden$(NC)" && exit 1)
	@ls $(KALI_IMAGE) >/dev/null 2>&1 || (echo "$(RED)❌ Kali Image nicht gefunden: $(KALI_IMAGE)$(NC)" && exit 1)
	@echo "$(GREEN)✅ Alle Abhängigkeiten verfügbar$(NC)"

install-deps: ## Installiert benötigte Abhängigkeiten (Ubuntu/Debian)
	@echo "$(BLUE)📦 Installiere Abhängigkeiten...$(NC)"
	sudo apt update
	sudo apt install -y kpartx xz-utils coreutils

build: check-deps ## Erstellt das r4w-linux Image
	@echo "$(GREEN)🔨 Erstelle r4w-linux v$(VERSION)...$(NC)"
	chmod +x build-r4w.sh
	sudo ./build-r4w.sh

clean: ## Räumt Build-Artefakte auf
	@echo "$(YELLOW)🧹 Räume auf...$(NC)"
	sudo umount r4w_build/boot 2>/dev/null || true
	sudo umount r4w_build/root 2>/dev/null || true
	sudo kpartx -d $(IMAGE_NAME) 2>/dev/null || true
	rm -rf r4w_build/
	rm -f $(IMAGE_NAME)
	@echo "$(GREEN)✅ Aufgeräumt$(NC)"

setup-ssh: ## Konfiguriert SSH für r4w-linux
	@echo "$(BLUE)🔑 Konfiguriere SSH...$(NC)"
	chmod +x scripts/setup-ssh.sh
	./scripts/setup-ssh.sh

flash-help: ## Zeigt Anleitung zum Flashen an
	@echo "$(GREEN)💾 Image flashen:$(NC)"
	@echo ""
	@echo "$(YELLOW)Windows (PowerShell):$(NC)"
	@echo "  .\\scripts\\flash-image.ps1"
	@echo ""
	@echo "$(YELLOW)Linux/macOS:$(NC)"
	@echo "  xzcat $(COMPRESSED_IMAGE) | sudo dd bs=4M of=/dev/sdX conv=fsync status=progress"
	@echo ""
	@echo "$(YELLOW)Balena Etcher (alle Systeme):$(NC)"
	@echo "  https://www.balena.io/etcher/"
	@echo ""
	@echo "$(RED)⚠️  Ersetze /dev/sdX mit dem korrekten Device!$(NC)"

verify: ## Prüft die Checksumme des Images
	@if [ -f "$(COMPRESSED_IMAGE).sha256" ]; then \
		echo "$(BLUE)🔐 Prüfe Checksumme...$(NC)"; \
		sha256sum -c $(COMPRESSED_IMAGE).sha256 && echo "$(GREEN)✅ Checksumme korrekt$(NC)" || echo "$(RED)❌ Checksumme falsch$(NC)"; \
	else \
		echo "$(YELLOW)⚠️  Keine Checksumme-Datei gefunden$(NC)"; \
	fi

package: build ## Erstellt ein Release-Paket
	@echo "$(BLUE)📦 Erstelle Release-Paket...$(NC)"
	mkdir -p release/r4w-linux-v$(VERSION)
	cp $(COMPRESSED_IMAGE) release/r4w-linux-v$(VERSION)/
	cp $(COMPRESSED_IMAGE).sha256 release/r4w-linux-v$(VERSION)/
	cp -r docs/ release/r4w-linux-v$(VERSION)/
	cp -r scripts/ release/r4w-linux-v$(VERSION)/
	cp -r ssh-config/ release/r4w-linux-v$(VERSION)/
	cp README.md CHANGELOG.md VERSION release/r4w-linux-v$(VERSION)/
	cd release && tar -czf r4w-linux-v$(VERSION).tar.gz r4w-linux-v$(VERSION)/
	@echo "$(GREEN)✅ Release-Paket erstellt: release/r4w-linux-v$(VERSION).tar.gz$(NC)"

test-ssh: ## Testet SSH-Verbindung zu r4w
	@echo "$(BLUE)🔍 Teste SSH-Verbindung...$(NC)"
	@if ping -c 1 -W 2 ************ >/dev/null 2>&1; then \
		echo "$(GREEN)✅ r4w erreichbar$(NC)"; \
		ssh -o ConnectTimeout=5 hack 'echo "$(GREEN)✅ SSH-Verbindung erfolgreich$(NC)"' || echo "$(RED)❌ SSH-Verbindung fehlgeschlagen$(NC)"; \
	else \
		echo "$(RED)❌ r4w nicht erreichbar unter ************$(NC)"; \
	fi

info: ## Zeigt Projekt-Informationen an
	@echo "$(GREEN)🐍 r4w-linux Projekt-Info$(NC)"
	@echo "$(GREEN)===========================$(NC)"
	@echo "Version: $(VERSION)"
	@echo "Image: $(COMPRESSED_IMAGE)"
	@echo "Größe: $(shell ls -lh $(COMPRESSED_IMAGE) 2>/dev/null | awk '{print $$5}' || echo 'nicht erstellt')"
	@echo "Checksumme: $(shell test -f $(COMPRESSED_IMAGE).sha256 && echo 'verfügbar' || echo 'nicht verfügbar')"
	@echo ""
	@echo "$(BLUE)Dateien:$(NC)"
	@find . -name "*.sh" -o -name "*.md" -o -name "*.ps1" | sort
	@echo ""
	@echo "$(YELLOW)🖤 designed by: you & your shell$(NC)"

# Versteckte Targets (ohne ## Kommentar)
.DEFAULT_GOAL := help
