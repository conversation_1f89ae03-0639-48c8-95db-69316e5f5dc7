# r4w-linux Image Flash Script für Windows
# PowerShell-Script zum Flashen des r4w-linux Images
# Version: 0.1.0

param(
    [Parameter(Mandatory=$false)]
    [string]$ImagePath = "r4w-linux.img.xz",
    
    [Parameter(Mandatory=$false)]
    [string]$DriveLetter,
    
    [Parameter(Mandatory=$false)]
    [switch]$Force
)

Write-Host "🐍 r4w-linux Image Flash Tool" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

# Prüfe ob Image existiert
if (-not (Test-Path $ImagePath)) {
    Write-Host "❌ Image-Datei nicht gefunden: $ImagePath" -ForegroundColor Red
    Write-Host "💡 Stelle sicher, dass die Datei im aktuellen Verzeichnis liegt." -ForegroundColor Yellow
    exit 1
}

# Prüfe Checksumme falls vorhanden
$checksumFile = "$ImagePath.sha256"
if (Test-Path $checksumFile) {
    Write-Host "🔐 Prüfe Checksumme..." -ForegroundColor Yellow
    $expectedHash = (Get-Content $checksumFile).Split(' ')[0]
    $actualHash = (Get-FileHash $ImagePath -Algorithm SHA256).Hash.ToLower()
    
    if ($expectedHash -eq $actualHash) {
        Write-Host "✅ Checksumme korrekt" -ForegroundColor Green
    } else {
        Write-Host "❌ Checksumme stimmt nicht überein!" -ForegroundColor Red
        if (-not $Force) {
            Write-Host "💡 Verwende -Force um trotzdem fortzufahren" -ForegroundColor Yellow
            exit 1
        }
    }
}

# Verfügbare Laufwerke anzeigen
Write-Host "`n💾 Verfügbare Wechseldatenträger:" -ForegroundColor Cyan
$removableDisks = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DriveType -eq 2 }

if ($removableDisks.Count -eq 0) {
    Write-Host "❌ Keine Wechseldatenträger gefunden!" -ForegroundColor Red
    Write-Host "💡 Stelle sicher, dass die SD-Karte eingelegt ist." -ForegroundColor Yellow
    exit 1
}

foreach ($disk in $removableDisks) {
    $sizeGB = [math]::Round($disk.Size / 1GB, 2)
    Write-Host "   $($disk.DeviceID) - $($disk.VolumeName) ($sizeGB GB)" -ForegroundColor White
}

# Laufwerk auswählen
if (-not $DriveLetter) {
    Write-Host "`n📝 Gib den Laufwerksbuchstaben der SD-Karte ein (z.B. E): " -NoNewline -ForegroundColor Yellow
    $DriveLetter = Read-Host
}

$DriveLetter = $DriveLetter.TrimEnd(':').ToUpper()
$targetDrive = "${DriveLetter}:"

# Prüfe ob Laufwerk existiert
if (-not (Test-Path $targetDrive)) {
    Write-Host "❌ Laufwerk $targetDrive nicht gefunden!" -ForegroundColor Red
    exit 1
}

# Warnung anzeigen
Write-Host "`n⚠️  WARNUNG: Alle Daten auf $targetDrive werden gelöscht!" -ForegroundColor Red
Write-Host "📁 Image: $ImagePath" -ForegroundColor White
Write-Host "💾 Ziel: $targetDrive" -ForegroundColor White

if (-not $Force) {
    Write-Host "`n❓ Fortfahren? (j/N): " -NoNewline -ForegroundColor Yellow
    $confirm = Read-Host
    if ($confirm -ne 'j' -and $confirm -ne 'J') {
        Write-Host "❌ Abgebrochen." -ForegroundColor Red
        exit 0
    }
}

# Prüfe ob Balena Etcher verfügbar ist
$etcherPath = Get-Command "balena-etcher" -ErrorAction SilentlyContinue
if ($etcherPath) {
    Write-Host "`n🔥 Starte Balena Etcher..." -ForegroundColor Green
    Start-Process -FilePath $etcherPath.Source -ArgumentList $ImagePath
    Write-Host "✅ Balena Etcher gestartet. Wähle dort die SD-Karte aus." -ForegroundColor Green
} else {
    Write-Host "`n❌ Balena Etcher nicht gefunden!" -ForegroundColor Red
    Write-Host "💡 Installiere Balena Etcher von: https://www.balena.io/etcher/" -ForegroundColor Yellow
    Write-Host "💡 Oder verwende ein anderes Tool zum Flashen." -ForegroundColor Yellow
    
    # Alternative: Win32DiskImager vorschlagen
    Write-Host "`n🔧 Alternative Tools:" -ForegroundColor Cyan
    Write-Host "   - Balena Etcher (empfohlen): https://www.balena.io/etcher/" -ForegroundColor White
    Write-Host "   - Win32 Disk Imager: https://sourceforge.net/projects/win32diskimager/" -ForegroundColor White
    Write-Host "   - Raspberry Pi Imager: https://www.raspberrypi.org/software/" -ForegroundColor White
}

Write-Host "`n📋 Nach dem Flashen:" -ForegroundColor Cyan
Write-Host "   1. SD-Karte wieder einlegen" -ForegroundColor White
Write-Host "   2. WLAN in wpa_supplicant.conf konfigurieren" -ForegroundColor White
Write-Host "   3. SSH-Key ersetzen" -ForegroundColor White
Write-Host "   4. ssh hack" -ForegroundColor White

Write-Host "`n🖤 designed by: you & your shell" -ForegroundColor Magenta
