# 📦 r4w-linux Installation

## Voraussetzungen

### Hardware
- Raspberry Pi Zero W v1.1
- microSD-Karte (mindestens 8GB, empfohlen 16GB+)
- microUSB-Kabel für Stromversorgung
- SD-Kartenleser

### Software
- [<PERSON><PERSON>](https://www.balena.io/etcher/) oder `dd` unter Linux
- SSH-Client (OpenSSH, PuTTY, etc.)
- Texteditor für Konfigurationsdateien

## Schritt-für-Schritt Installation

### 1. Image herunterladen

Lade das aktuelle r4w-linux Image herunter:
- `r4w-linux.img.xz` (komprimiertes Image)
- `r4w-linux.img.xz.sha256` (Checksumme)

Prüfe die Integrität:
```bash
sha256sum -c r4w-linux.img.xz.sha256
```

### 2. Image auf SD-Karte flashen

#### Mi<PERSON> <PERSON><PERSON> (empfohlen)
1. <PERSON><PERSON> starten
2. `r4w-linux.img.xz` auswählen
3. SD-Karte auswählen
4. "Flash!" klicken

#### Mit dd (Linux/macOS)
```bash
xzcat r4w-linux.img.xz | sudo dd bs=4M of=/dev/sdX conv=fsync status=progress
```
⚠️ Ersetze `/dev/sdX` mit dem korrekten Device deiner SD-Karte!

### 3. WLAN konfigurieren

Nach dem Flashen:
1. SD-Karte wieder in den PC einlegen
2. Auf der `boot`-Partition die Datei `wpa_supplicant.conf` bearbeiten
3. WLAN-Daten eintragen:

```conf
country=DE
ctrl_interface=DIR=/var/run/wpa_supplicant GROUP=netdev
update_config=1

network={
    ssid="DEIN_WLAN_NAME"
    psk="DEIN_WLAN_PASSWORT"
    key_mgmt=WPA-PSK
}
```

### 4. SSH-Zugang einrichten

#### SSH-Config kopieren
Kopiere die Datei `ssh-config/config` in deine SSH-Konfiguration:

**Windows:**
```powershell
Copy-Item ssh-config\config $env:USERPROFILE\.ssh\config
```

**Linux/macOS:**
```bash
cp ssh-config/config ~/.ssh/config
```

#### SSH-Key generieren
```bash
ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_r4w
```

### 5. Raspberry Pi starten

1. SD-Karte in den Raspberry Pi einlegen
2. Stromversorgung anschließen
3. 2-3 Minuten warten (erster Boot dauert länger)

### 6. SSH-Verbindung testen

```bash
ssh hack
```

Beim ersten Login:
- Passwort: `toor`
- **Sofort ändern:** `passwd`

### 7. SSH-Key ersetzen

Den Dummy-Key durch deinen eigenen ersetzen:

```bash
# Public Key auf den Pi kopieren
scp ~/.ssh/id_r4w.pub kali@************:/home/<USER>/.ssh/authorized_keys

# Passwort-Login deaktivieren (bereits vorkonfiguriert)
```

## Troubleshooting

### Pi nicht erreichbar
1. Router-Interface prüfen (meist ***********)
2. DHCP-Client-Liste nach "r4w" oder "kali" suchen
3. Netzwerk-Scan: `nmap -sn ***********/24`

### SSH-Verbindung fehlschlägt
1. IP-Adresse prüfen
2. SSH-Key-Pfad in `~/.ssh/config` kontrollieren
3. Firewall-Einstellungen prüfen

### WLAN-Verbindung fehlschlägt
1. SSID und Passwort in `wpa_supplicant.conf` prüfen
2. 5GHz vs. 2.4GHz (Pi Zero W unterstützt nur 2.4GHz)
3. Sonderzeichen im Passwort escapen

## Nächste Schritte

Nach erfolgreicher Installation:
- [Tools](TOOLS.md) installieren und konfigurieren
- [Sicherheit](SECURITY.md) härten
