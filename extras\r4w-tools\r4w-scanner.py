#!/usr/bin/env python3
# r4w-scanner.py: Multi-Purpose Web Vulnerability Scanner
# Version: 1.0.0
# Autor: r4w-linux project
# Verwendung: python3 r4w-scanner.py

import requests
import urllib3
import sys
import time
from urllib.parse import urljoin, urlparse

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class R4WScanner:
    def __init__(self):
        self.session = requests.Session()
        self.session.verify = False
        self.session.timeout = 10
        self.session.headers.update({
            'User-Agent': 'r4w-scanner/1.0 (Educational Purpose Only)'
        })
        
    def banner(self):
        print("""
    ██████╗ ██╗  ██╗██╗    ██╗      ███████╗ ██████╗ █████╗ ███╗   ██╗███╗   ██╗███████╗██████╗
    ██╔══██╗██║  ██║██║    ██║      ██╔════╝██╔════╝██╔══██╗████╗  ██║████╗  ██║██╔════╝██╔══██╗
    ██████╔╝███████║██║ █╗ ██║█████╗███████╗██║     ███████║██╔██╗ ██║██╔██╗ ██║█████╗  ██████╔╝
    ██╔══██╗╚════██║██║███╗██║╚════╝╚════██║██║     ██╔══██║██║╚██╗██║██║╚██╗██║██╔══╝  ██╔══██╗
    ██║  ██║     ██║╚███╔███╔╝      ███████║╚██████╗██║  ██║██║ ╚████║██║ ╚████║███████╗██║  ██║
    ╚═╝  ╚═╝     ╚═╝ ╚══╝╚══╝       ╚══════╝ ╚═════╝╚═╝  ╚═╝╚═╝  ╚═══╝╚═╝  ╚═══╝╚══════╝╚═╝  ╚═╝
        """)
        print("    🕷️  Multi-Purpose Web Vulnerability Scanner v1.0")
        print("    ⚠️  Nur für Bildungszwecke und autorisierte Tests!")
        print("    🎯 XSS • LFI • SQLi • Directory Traversal • Open Redirect")
        print("")
        
    def get_target_url(self):
        while True:
            url = input("🎯 Ziel-URL eingeben: ").strip()
            if not url:
                print("❌ URL darf nicht leer sein!")
                continue
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            try:
                parsed = urlparse(url)
                if parsed.netloc:
                    return url
                else:
                    print("❌ Ungültige URL!")
            except:
                print("❌ Ungültige URL!")
                
    def show_menu(self):
        print("\n🔧 Verfügbare Tests:")
        print("1. XSS Scanner (Reflected)")
        print("2. LFI Scanner (Local File Inclusion)")
        print("3. SQL Injection Scanner (Basic)")
        print("4. Directory Traversal Scanner")
        print("5. Open Redirect Scanner")
        print("6. Alle Tests ausführen")
        print("0. Beenden")
        print("")
        
    def xss_scanner(self, base_url):
        print("🔍 Starte XSS Scanner...")
        
        xss_payloads = [
            "<script>alert('r4w-xss')</script>",
            "<img src=x onerror=alert('r4w-xss')>",
            "javascript:alert('r4w-xss')",
            "<svg onload=alert('r4w-xss')>",
            "'\"><script>alert('r4w-xss')</script>"
        ]
        
        test_params = ['q', 'search', 'query', 'keyword', 'term', 'input', 'data']
        
        vulnerabilities = []
        
        for param in test_params:
            for payload in xss_payloads:
                test_url = f"{base_url}?{param}={payload}"
                try:
                    response = self.session.get(test_url)
                    if payload in response.text and response.status_code == 200:
                        vulnerabilities.append({
                            'type': 'XSS',
                            'url': test_url,
                            'param': param,
                            'payload': payload
                        })
                        print(f"[✅] XSS gefunden: {param} → {payload[:30]}...")
                    time.sleep(0.5)  # Rate limiting
                except Exception as e:
                    print(f"[⚠️] Fehler bei {test_url}: {e}")
                    
        return vulnerabilities
        
    def lfi_scanner(self, base_url):
        print("🔍 Starte LFI Scanner...")
        
        lfi_payloads = [
            "../../../../etc/passwd",
            "..\\..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
            "/etc/passwd",
            "C:\\windows\\system32\\drivers\\etc\\hosts",
            "../../../../proc/version",
            "../../../../etc/shadow"
        ]
        
        test_params = ['file', 'page', 'include', 'view', 'template', 'doc', 'path']
        indicators = ['root:x:', 'daemon:', 'bin:', 'sys:', 'localhost', 'Linux version']
        
        vulnerabilities = []
        
        for param in test_params:
            for payload in lfi_payloads:
                test_url = f"{base_url}?{param}={payload}"
                try:
                    response = self.session.get(test_url)
                    for indicator in indicators:
                        if indicator in response.text and response.status_code == 200:
                            vulnerabilities.append({
                                'type': 'LFI',
                                'url': test_url,
                                'param': param,
                                'payload': payload,
                                'indicator': indicator
                            })
                            print(f"[✅] LFI gefunden: {param} → {indicator}")
                            break
                    time.sleep(0.5)
                except Exception as e:
                    print(f"[⚠️] Fehler bei {test_url}: {e}")
                    
        return vulnerabilities
        
    def sqli_scanner(self, base_url):
        print("🔍 Starte SQL Injection Scanner...")
        
        sqli_payloads = [
            "'",
            "' OR '1'='1",
            "' OR 1=1--",
            "' UNION SELECT NULL--",
            "1' AND 1=1--",
            "1' AND 1=2--"
        ]
        
        test_params = ['id', 'user', 'search', 'category', 'product', 'item']
        error_indicators = [
            'mysql_fetch_array',
            'ORA-01756',
            'Microsoft OLE DB',
            'SQLServer JDBC Driver',
            'PostgreSQL query failed',
            'Warning: pg_',
            'valid MySQL result',
            'MySqlClient.'
        ]
        
        vulnerabilities = []
        
        for param in test_params:
            for payload in sqli_payloads:
                test_url = f"{base_url}?{param}={payload}"
                try:
                    response = self.session.get(test_url)
                    for indicator in error_indicators:
                        if indicator.lower() in response.text.lower():
                            vulnerabilities.append({
                                'type': 'SQLi',
                                'url': test_url,
                                'param': param,
                                'payload': payload,
                                'indicator': indicator
                            })
                            print(f"[✅] SQL Injection gefunden: {param} → {indicator}")
                            break
                    time.sleep(0.5)
                except Exception as e:
                    print(f"[⚠️] Fehler bei {test_url}: {e}")
                    
        return vulnerabilities
        
    def directory_traversal_scanner(self, base_url):
        print("🔍 Starte Directory Traversal Scanner...")
        
        traversal_payloads = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\win.ini",
            "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
            "....//....//....//etc/passwd"
        ]
        
        test_params = ['file', 'path', 'dir', 'folder', 'document']
        indicators = ['root:x:', '[fonts]', 'daemon:', 'bin:']
        
        vulnerabilities = []
        
        for param in test_params:
            for payload in traversal_payloads:
                test_url = f"{base_url}?{param}={payload}"
                try:
                    response = self.session.get(test_url)
                    for indicator in indicators:
                        if indicator in response.text:
                            vulnerabilities.append({
                                'type': 'Directory Traversal',
                                'url': test_url,
                                'param': param,
                                'payload': payload,
                                'indicator': indicator
                            })
                            print(f"[✅] Directory Traversal gefunden: {param} → {indicator}")
                            break
                    time.sleep(0.5)
                except Exception as e:
                    print(f"[⚠️] Fehler bei {test_url}: {e}")
                    
        return vulnerabilities
        
    def open_redirect_scanner(self, base_url):
        print("🔍 Starte Open Redirect Scanner...")
        
        redirect_payloads = [
            "http://evil.com",
            "https://google.com",
            "//evil.com",
            "javascript:alert('redirect')"
        ]
        
        test_params = ['redirect', 'url', 'next', 'return', 'goto', 'target']
        
        vulnerabilities = []
        
        for param in test_params:
            for payload in redirect_payloads:
                test_url = f"{base_url}?{param}={payload}"
                try:
                    response = self.session.get(test_url, allow_redirects=False)
                    if response.status_code in [301, 302, 303, 307, 308]:
                        location = response.headers.get('Location', '')
                        if payload in location or 'evil.com' in location or 'google.com' in location:
                            vulnerabilities.append({
                                'type': 'Open Redirect',
                                'url': test_url,
                                'param': param,
                                'payload': payload,
                                'redirect_to': location
                            })
                            print(f"[✅] Open Redirect gefunden: {param} → {location}")
                    time.sleep(0.5)
                except Exception as e:
                    print(f"[⚠️] Fehler bei {test_url}: {e}")
                    
        return vulnerabilities
        
    def generate_report(self, vulnerabilities):
        if not vulnerabilities:
            print("\n✅ Keine Schwachstellen gefunden!")
            return
            
        print(f"\n📋 Scan-Report: {len(vulnerabilities)} Schwachstelle(n) gefunden")
        print("=" * 60)
        
        for i, vuln in enumerate(vulnerabilities, 1):
            print(f"\n{i}. {vuln['type']}")
            print(f"   URL: {vuln['url']}")
            print(f"   Parameter: {vuln['param']}")
            print(f"   Payload: {vuln['payload']}")
            if 'indicator' in vuln:
                print(f"   Indikator: {vuln['indicator']}")
            if 'redirect_to' in vuln:
                print(f"   Weiterleitung: {vuln['redirect_to']}")
                
    def run(self):
        self.banner()
        target_url = self.get_target_url()
        
        print(f"\n🎯 Ziel: {target_url}")
        
        all_vulnerabilities = []
        
        while True:
            self.show_menu()
            choice = input("Auswahl: ").strip()
            
            if choice == '0':
                print("👋 Auf Wiedersehen!")
                break
            elif choice == '1':
                vulns = self.xss_scanner(target_url)
                all_vulnerabilities.extend(vulns)
            elif choice == '2':
                vulns = self.lfi_scanner(target_url)
                all_vulnerabilities.extend(vulns)
            elif choice == '3':
                vulns = self.sqli_scanner(target_url)
                all_vulnerabilities.extend(vulns)
            elif choice == '4':
                vulns = self.directory_traversal_scanner(target_url)
                all_vulnerabilities.extend(vulns)
            elif choice == '5':
                vulns = self.open_redirect_scanner(target_url)
                all_vulnerabilities.extend(vulns)
            elif choice == '6':
                print("🚀 Führe alle Tests aus...")
                vulns = []
                vulns.extend(self.xss_scanner(target_url))
                vulns.extend(self.lfi_scanner(target_url))
                vulns.extend(self.sqli_scanner(target_url))
                vulns.extend(self.directory_traversal_scanner(target_url))
                vulns.extend(self.open_redirect_scanner(target_url))
                all_vulnerabilities.extend(vulns)
                self.generate_report(vulns)
            else:
                print("❌ Ungültige Auswahl!")
                
if __name__ == "__main__":
    scanner = R4WScanner()
    try:
        scanner.run()
    except KeyboardInterrupt:
        print("\n\n👋 Scanner beendet!")
    except Exception as e:
        print(f"\n❌ Fehler: {e}")
