# r4w-linux Native Windows Builder
# PowerShell Script zum Erstellen eines konfigurierten Kali Linux Images
# Version: 0.1.0 - Native Windows ohne WSL2
# Autor: r4w-linux project
# Benoetigt: Windows 10/11 mit Administrator-Rechten

param(
    [Parameter(Mandatory=$false)]
    [string]$KaliImage = "",
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipChecks = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$VerboseOutput = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$KeepTempFiles = $false
)

$ErrorActionPreference = "Stop"
$version = "0.1.0"

Write-Host "r4w-linux Native Windows Builder v$version" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green
Write-Host "Kein WSL2 erforderlich - Native Windows Build!" -ForegroundColor Cyan
Write-Host ""

# Administrator-Rechte pruefen
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "Administrator-Rechte erforderlich!" -ForegroundColor Red
    Write-Host "Starte PowerShell als Administrator und fuehre das Script erneut aus." -ForegroundColor Yellow
    exit 1
}

# Pruefe ob 7-Zip verfuegbar ist
Write-Host "Pruefe 7-Zip..." -ForegroundColor Yellow
$sevenZipPaths = @(
    "${env:ProgramFiles}\7-Zip\7z.exe",
    "${env:ProgramFiles(x86)}\7-Zip\7z.exe",
    "7z.exe"
)

$sevenZipPath = $null
foreach ($path in $sevenZipPaths) {
    if (Test-Path $path -ErrorAction SilentlyContinue) {
        $sevenZipPath = $path
        break
    }
}

if (-not $sevenZipPath) {
    Write-Host "7-Zip nicht gefunden!" -ForegroundColor Red
    Write-Host "Installiere 7-Zip von: https://www.7-zip.org/" -ForegroundColor Yellow
    Write-Host "   Oder installiere mit: winget install 7zip.7zip" -ForegroundColor White
    exit 1
}

Write-Host "7-Zip gefunden: $sevenZipPath" -ForegroundColor Green

# Pruefe ob Kali Image vorhanden
Write-Host "Suche Kali Linux Image..." -ForegroundColor Yellow
if ($KaliImage -eq "") {
    # Suche nach .img.xz und .img Dateien
    $kaliFiles = @()
    $kaliFiles += Get-ChildItem -Path . -Filter "kali-linux-*-raspberry-pi-zero-w.img.xz" -ErrorAction SilentlyContinue
    $kaliFiles += Get-ChildItem -Path . -Filter "kali-linux-*-raspberry-pi-zero-w.img" -ErrorAction SilentlyContinue
    
    if ($kaliFiles.Count -eq 0) {
        Write-Host "Kein Kali Linux Image gefunden!" -ForegroundColor Red
        Write-Host "Lade es von: https://www.kali.org/get-kali/#kali-arm" -ForegroundColor Yellow
        Write-Host "   Dateiname sollte sein: kali-linux-*-raspberry-pi-zero-w.img.xz oder .img" -ForegroundColor White
        exit 1
    }
    $KaliImage = $kaliFiles[0].Name
}

if (-not (Test-Path $KaliImage)) {
    Write-Host "Kali Image nicht gefunden: $KaliImage" -ForegroundColor Red
    exit 1
}

Write-Host "Kali Image gefunden: $KaliImage" -ForegroundColor Green

# Konfiguration
$workDir = "r4w_build_windows"
$outputImg = "r4w-linux.img"
$tempImg = "temp_kali.img"
$versionFile = "VERSION"
$projectVersion = if (Test-Path $versionFile) { Get-Content $versionFile } else { "0.1.0" }

Write-Host "Konfiguration:" -ForegroundColor Blue
Write-Host "   Arbeitsverzeichnis: $workDir" -ForegroundColor White
Write-Host "   Output Image: $outputImg" -ForegroundColor White
Write-Host "   Version: $projectVersion" -ForegroundColor White
Write-Host ""

# Cleanup function
function Cleanup {
    Write-Host "Cleanup wird ausgefuehrt..." -ForegroundColor Yellow
    
    # Dismount any mounted images
    try {
        # Check if temp or output images exist and are mounted
        if (Test-Path $tempImg) {
            $tempImgFull = (Resolve-Path $tempImg -ErrorAction SilentlyContinue).Path
            if ($tempImgFull) {
                Get-DiskImage -ImagePath $tempImgFull -ErrorAction SilentlyContinue | Dismount-DiskImage -ErrorAction SilentlyContinue
            }
        }
        if (Test-Path $outputImg) {
            $outputImgFull = (Resolve-Path $outputImg -ErrorAction SilentlyContinue).Path
            if ($outputImgFull) {
                Get-DiskImage -ImagePath $outputImgFull -ErrorAction SilentlyContinue | Dismount-DiskImage -ErrorAction SilentlyContinue
            }
        }
    } catch {
        # Ignore errors during cleanup
    }
    
    if (-not $KeepTempFiles) {
        if (Test-Path $workDir) {
            Remove-Item -Path $workDir -Recurse -Force -ErrorAction SilentlyContinue
        }
        if (Test-Path $tempImg) {
            Remove-Item -Path $tempImg -Force -ErrorAction SilentlyContinue
        }
    }
}

# Set trap for cleanup on exit
trap { Cleanup; exit 1 }

# Workspace vorbereiten
Write-Host "Workspace vorbereiten..." -ForegroundColor Yellow
Cleanup

# Arbeitsverzeichnis erstellen
if (-not (Test-Path $workDir)) {
    New-Item -ItemType Directory -Path $workDir | Out-Null
}

Write-Host "Extrahiere Kali Image..." -ForegroundColor Yellow

# Bestimme ob das Image komprimiert ist
$isCompressed = $KaliImage.EndsWith(".xz")
if ($isCompressed) {
    # Extrahiere .xz Datei
    $extractedImg = $KaliImage -replace "\.xz$", ""
    if (Test-Path $extractedImg) {
        Remove-Item $extractedImg -Force
    }
    
    Write-Host "Extrahiere $KaliImage..." -ForegroundColor Yellow
    & $sevenZipPath x $KaliImage -y
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Fehler beim Extrahieren!" -ForegroundColor Red
        exit 1
    }
    $sourceImg = $extractedImg
} else {
    $sourceImg = $KaliImage
}

# Kopiere Image fuer Bearbeitung
Write-Host "Kopiere Image fuer Bearbeitung..." -ForegroundColor Yellow
Copy-Item $sourceImg $tempImg

Write-Host ""
Write-Host "r4w-linux v$projectVersion erfolgreich vorbereitet!" -ForegroundColor Green
Write-Host "Naechste Schritte:" -ForegroundColor Yellow
Write-Host "   1. Image mit Balena Etcher flashen" -ForegroundColor White
Write-Host "   2. WLAN in wpa_supplicant.conf konfigurieren" -ForegroundColor White
Write-Host "   3. SSH-Key nach dem ersten Boot konfigurieren" -ForegroundColor White
Write-Host "   4. ssh kali@************" -ForegroundColor White
Write-Host ""
Write-Host "Hinweis: Fuer vollstaendige r4w-Konfiguration fuehre nach dem" -ForegroundColor Yellow
Write-Host "   ersten Boot das Setup-Script auf dem Pi aus:" -ForegroundColor Yellow
Write-Host "   sudo /home/<USER>/extras/autostart.sh" -ForegroundColor White
Write-Host ""

# Cleanup
Cleanup
