# 🐍 r4w-tools - Custom Hacking Suite

Eine Sammlung von maßgeschneiderten Penetration Testing Tools für r4w-linux.

## 🎯 Übersicht

Die r4w-tools sind speziell für r4w-linux entwickelte Python-Scripts, die häufige Penetration Testing Aufgaben automatisieren und vereinfachen.

## 🔧 Verfügbare Tools

### 🕷️ r4w-menu.py
**Hauptmenü für alle r4w-tools**
- Zentrale Benutzeroberfläche
- Kategorisierte Tool-Auswahl
- Integrierte Parameter-Eingabe
- Tool-Status Übersicht

```bash
# Starten
python3 r4w-menu.py
# oder
r4w
```

### 🔍 r4w-scanner.py
**Multi-Purpose Web Vulnerability Scanner**
- XSS Detection (Reflected)
- LFI (Local File Inclusion) Scanner
- SQL Injection Detection
- Directory Traversal Scanner
- Open Redirect Scanner

```bash
# Starten
python3 r4w-scanner.py
# oder
r4w-scan
```

**Features:**
- Automatische Parameter-Erkennung
- Multiple Payload-Tests
- Detaillierte Vulnerability Reports
- Rate Limiting für stealth

### 🎭 r4w-xss-checker.py
**Advanced XSS Detection mit Browser Automation**
- Reflected XSS Testing
- Stored XSS Detection
- DOM XSS Analysis
- Custom URL Testing
- Browser-basierte Alert Detection

```bash
# Starten
python3 r4w-xss-checker.py
# oder
r4w-xss
```

**Voraussetzungen:**
- Playwright: `pip3 install playwright && playwright install chromium`

### 🔍 r4w-recon.py
**Automated Reconnaissance Tool**
- WHOIS Lookup
- DNS Enumeration
- Port Scanning
- Web Enumeration
- SSL/TLS Analysis
- Vulnerability Scanning

```bash
# Starten
python3 r4w-recon.py
# oder
r4w-recon
```

**Features:**
- Vollautomatischer Reconnaissance
- Strukturierte Output-Files
- Detaillierte Reports
- Subdomain Enumeration

### 💉 r4w-payloads.py
**Payload Generator & Encoder**
- XSS Payloads
- SQL Injection Payloads
- LFI Payloads
- RCE Payloads
- XXE Payloads
- SSTI Payloads

```bash
# Starten
python3 r4w-payloads.py
# oder
r4w-payloads
```

**Encoding Optionen:**
- URL Encoding (Single/Double)
- Base64 Encoding
- HTML Encoding
- Hex Encoding
- Unicode Encoding

## 📚 Wordlists

### Automatisch installierte Wordlists:
- **rockyou.txt** - Beliebteste Passwort-Liste
- **SecLists** - Umfangreiche Security Testing Listen
- **Common Passwords** - Basis-Passwort-Liste
- **Common Usernames** - Standard-Benutzernamen

### Wordlist-Pfade:
```bash
/usr/share/wordlists/rockyou.txt
/usr/share/wordlists/SecLists/
/usr/share/wordlists/common-passwords.txt
/usr/share/wordlists/common-usernames.txt
```

## 🚀 Quick Start

### 1. r4w-tools starten
```bash
# Hauptmenü öffnen
r4w

# Oder direkt ein Tool starten
r4w-scan
r4w-xss
r4w-recon
r4w-payloads
```

### 2. Web Application Testing
```bash
# 1. Reconnaissance
r4w-recon
# Ziel eingeben: example.com

# 2. Vulnerability Scanning
r4w-scan
# URL eingeben: https://example.com

# 3. XSS Testing
r4w-xss
# URL eingeben und Tests auswählen
```

### 3. Payload Generation
```bash
# Payloads generieren
r4w-payloads
# Kategorie auswählen (XSS, SQLi, etc.)
# Payload kopieren und encodieren
```

## 🔧 Integration mit Standard-Tools

Die r4w-tools sind nahtlos in das r4w-linux Ökosystem integriert:

### Web Testing Workflow:
1. **r4w-recon** → Reconnaissance & Port Scanning
2. **r4w-scanner** → Basic Vulnerability Detection
3. **r4w-xss** → Advanced XSS Testing
4. **SQLMap** → SQL Injection Exploitation
5. **Nikto/Gobuster** → Directory Enumeration

### Network Testing Workflow:
1. **r4w-recon** → Target Discovery
2. **Nmap** → Detailed Port Scanning
3. **Masscan** → Fast Network Scanning
4. **Hydra** → Brute Force Attacks

## 📋 Tool-Kategorien im Hauptmenü

### 🕷️ Web Application Security
- r4w-scanner, r4w-xss-checker, r4w-payloads
- SQLMap, Nikto, Gobuster, WPScan, Dirb

### 🌐 Network Reconnaissance
- r4w-recon, Nmap, Masscan
- Netcat, Socat, Chisel, SSHuttle

### 🔐 Password Attacks
- Hydra, John the Ripper, Hashcat
- Medusa, CrackMapExec

### 📡 Wireless Security
- Aircrack-ng Suite, Wifite
- Reaver, Pixiewps, Macchanger

### 🗂️ Wordlist Management
- Download rockyou.txt, SecLists, FuzzDB
- Custom Wordlist Creation
- Wordlist Statistics

### ⚙️ System Tools
- Metasploit, Tor Browser, ProxyChains
- Wireshark/TShark, System Updates

## 🛡️ Sicherheitshinweise

⚠️ **Wichtige Hinweise:**
- Alle Tools sind ausschließlich für **Bildungszwecke** und **autorisierte Penetration Tests**
- Verwende die Tools nur gegen eigene Systeme oder mit expliziter Erlaubnis
- Beachte lokale Gesetze und Bestimmungen
- Rate Limiting ist implementiert, aber sei trotzdem vorsichtig

## 🔄 Updates & Erweiterungen

### Custom Payloads hinzufügen:
```bash
# In r4w-payloads.py
# Option "3. Custom Payload hinzufügen" verwenden
```

### Neue Tools integrieren:
```bash
# Script in /home/<USER>/extras/r4w-tools/ ablegen
chmod +x new-tool.py

# In r4w-menu.py integrieren
# Alias in .zshrc hinzufügen
```

## 📊 Output & Reports

### r4w-recon Output:
```
/tmp/r4w-recon-target-timestamp/
├── whois.txt
├── dns_a.txt
├── nmap_tcp_quick.txt
├── nikto_80.txt
└── recon_report.txt
```

### r4w-scanner Output:
- Konsolen-Output mit farbigen Status-Meldungen
- Detaillierte Vulnerability Reports
- Payload-spezifische Informationen

## 🎓 Learning Resources

### Empfohlene Lernreihenfolge:
1. **r4w-recon** → Grundlagen der Reconnaissance
2. **r4w-scanner** → Web Vulnerability Basics
3. **r4w-payloads** → Payload Crafting
4. **r4w-xss** → Advanced Browser-based Testing

### Weiterführende Tools:
- Burp Suite (für manuelle Tests)
- OWASP ZAP (für automatisierte Scans)
- Metasploit (für Exploitation)

## 🤝 Contribution

Verbesserungen und neue Tools sind willkommen!

### Entwicklungsrichtlinien:
- Python 3.x kompatibel
- Deutsche Benutzeroberfläche
- Farbige Konsolen-Ausgabe
- Error Handling implementieren
- Educational Purpose Disclaimer

---

**Happy coding! 🐍**

*r4w-tools - Teil des r4w-linux Projekts*
